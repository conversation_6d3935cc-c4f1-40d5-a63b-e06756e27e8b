import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Image, Space, Tag, message, Switch, Typography, Button, Tooltip } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import type { FeedbackRecord, FeedbackQueryParams } from '@/services/ant-design-pro/feedback';
import { queryFeedback } from '@/services/ant-design-pro/feedback';
import * as XLSX from 'xlsx';

// 挽留原因映射表
const LEAVE_REASON_MAP: Record<string, string> = {
  'A0': '其它',
  'A1': '只是随便逛逛，暂无点餐需求',
  'A2': '理解需求不够准确',
  'A3': '推荐餐品不符合我的口味偏好',
  'A4': '推荐餐品价格偏高',
  'A5': '操作流程复杂',
  'B0': '其它',
  'B1': '只是随便逛逛，暂无点餐需求',
  'B2': '理解需求不够准确',
  'B3': '推荐餐品不符合我的口味偏好',
  'B4': '推荐餐品价格偏高',
  'B5': '操作流程复杂'
};

// 挽留原因颜色映射表 - 每个key对应一种颜色
const LEAVE_REASON_COLOR_MAP: Record<string, string> = {
  'A0': 'default',      // 其它
  'A1': 'blue',         // 只是随便逛逛，暂无点餐需求
  'A2': 'green',        // 理解需求不够准确
  'A3': 'orange',       // 推荐餐品不符合我的口味偏好
  'A4': 'red',          // 推荐餐品价格偏高
  'A5': 'purple',       // 操作流程复杂
  'B0': 'geekblue',     // 其它
  'B1': 'cyan',         // 只是随便逛逛，暂无点餐需求
  'B2': 'lime',         // 理解需求不够准确
  'B3': 'gold',         // 推荐餐品不符合我的口味偏好
  'B4': 'volcano',      // 推荐餐品价格偏高
  'B5': 'magenta'       // 操作流程复杂
};

/**
 * AI点餐挽留统计页面
 */
const AiLeaveReasonList: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  
  // 环境切换状态，默认为生产环境
  const [isProduction, setIsProduction] = useState(true);
  
  // 选中行状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<FeedbackRecord[]>([]);
  
  // 存储当前查询参数，用于导出功能
  const [currentQueryParams, setCurrentQueryParams] = useState<FeedbackQueryParams>({
    pageNo: 1,
    pageSize: 20,
  });

  // 解析图片URL列表
  const parseImageUrls = (imgUrl: string): string[] => {
    try {
      if (!imgUrl || imgUrl === '[]') return [];
      return JSON.parse(imgUrl);
    } catch (error) {
      return [];
    }
  };

  // 解析挽留原因类型
  const parseLeaveReasonType = (type: string): string => {
    if (!type) return '未知';

    // 如果type是逗号分隔的字符串，解析每个数字并映射
    const typeNumbers = type.split(',').map(num => num.trim()).filter(num => num);
    const mappedReasons = typeNumbers.map(num => LEAVE_REASON_MAP[num] || `未知类型(${num})`);

    return mappedReasons.join(', ');
  };

  // 渲染挽留原因标签
  const renderLeaveReasonTags = (type: string) => {
    if (!type) return <Tag color="default">未知</Tag>;

    // 如果type是逗号分隔的字符串，解析每个数字并映射
    const typeNumbers = type.split(',').map(num => num.trim()).filter(num => num);

    if (typeNumbers.length === 0) {
      return <Tag color="default">未知</Tag>;
    }

    return (
      <Space wrap size={[4, 4]}>
        {typeNumbers.map((num, index) => {
          const reasonText = LEAVE_REASON_MAP[num] || `未知类型(${num})`;
          const color = LEAVE_REASON_COLOR_MAP[num] || 'default';

          return (
            <Tag
              key={`${num}-${index}`}
              color={color}
              style={{
                margin: '2px',
                fontSize: '12px',
                lineHeight: '20px',
                maxWidth: '150px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              <Tooltip title={reasonText} placement="top">
                {reasonText}
              </Tooltip>
            </Tag>
          );
        })}
      </Space>
    );
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: number): string => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 导出Excel功能
  const handleExport = async (exportAll: boolean = false) => {
    const hide = message.loading('正在导出数据...', 0);
    
    try {
      let dataToExport: FeedbackRecord[] = [];
      
      if (exportAll) {
        // 导出所有数据 - 分页获取
        let currentPage = 1;
        const pageSize = 100; // 每次获取100条
        let hasMore = true;
        let allData: FeedbackRecord[] = [];
        
        while (hasMore) {
          const queryParams = {
            ...currentQueryParams,
            pageNo: currentPage,
            pageSize: pageSize,
          };
          
          console.log(`正在获取第${currentPage}页数据...`);
          const response = await queryFeedback(queryParams, isProduction);

          if (response && response.errCode === 0 && response.data.list) {
            // 过滤feedbackType为"leaveReason"的数据
            const filteredData = response.data.list.filter(item =>
              item.feedbackType === 'leaveReason'
            );
            allData.push(...filteredData);
            
            // 检查是否还有更多数据
            hasMore = response.data.hasMore || (response.data.list.length === pageSize);
            currentPage++;
            
            console.log(`已获取${allData.length}条数据，是否还有更多: ${hasMore}`);
          } else {
            hasMore = false;
            if (response?.errCode !== 0) {
              throw new Error('获取数据失败');
            }
          }
        }
        
        dataToExport = allData;
        console.log(`数据获取完成，总共${dataToExport.length}条记录`);
      } else {
        // 导出选中数据
        dataToExport = selectedRows;
      }
      
      if (dataToExport.length === 0) {
        hide();
        message.warning('没有数据可导出');
        return;
      }
      
      // 字段映射关系 - 与页面显示的列名保持一致
      const fieldMapping: Record<string, string> = {
        'lastupdatetime': '反馈时间',
        'id': 'ID',
        'phone': '手机号',
        'sessionId': 'Session ID',
        'type': '挽留原因',
        'content': '反馈内容',
        'imgUrl': '图片列表',
        'videoUrl': '视频',
      };
      
      // 转换数据格式
      const exportData = dataToExport.map(record => {
        const row: Record<string, any> = {};
        
        Object.entries(fieldMapping).forEach(([key, label]) => {
          if (key === 'lastupdatetime') {
            row[label] = formatTimestamp(record[key as keyof FeedbackRecord] as number);
          } else if (key === 'type') {
            // 对于挽留统计页面，使用特殊的类型解析
            row[label] = parseLeaveReasonType(record.type);
          } else if (key === 'imgUrl') {
            const urls = parseImageUrls(record.imgUrl);
            row[label] = urls.length > 0 ? urls.join(', ') : '无';
          } else if (key === 'videoUrl') {
            row[label] = record.videoUrl || '无';
          } else {
            row[label] = record[key as keyof FeedbackRecord] || '';
          }
        });
        
        return row;
      });
      
      // 创建工作簿
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'AI点餐挽留统计');
      
      // 生成文件名
      const now = new Date();
      const fileName = `ai_leave_reason_${now.getFullYear()}_${String(now.getMonth() + 1).padStart(2, '0')}_${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}_${String(now.getMinutes()).padStart(2, '0')}_${String(now.getSeconds()).padStart(2, '0')}.xlsx`;
      
      // 下载文件
      XLSX.writeFile(wb, fileName);
      
      hide();
      message.success(`成功导出 ${dataToExport.length} 条记录`);
      
    } catch (error) {
      hide();
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };



  // 表格工具栏渲染
  const TableAlertRender = () => (
    <Space size={16}>
      <span>
        已选择 <strong>{selectedRowKeys.length}</strong> 项
      </span>
      <Button
        type="primary"
        icon={<ExportOutlined />}
        onClick={() => handleExport(false)}
        disabled={selectedRowKeys.length === 0}
      >
        导出选中
      </Button>
      <Button
        icon={<ExportOutlined />}
        onClick={() => handleExport(true)}
      >
        导出全部
      </Button>
    </Space>
  );

  // 表格列定义
  const columns: ProColumns<FeedbackRecord>[] = [
    {
      title: '反馈时间',
      dataIndex: 'lastupdatetime',
      width: 180,
      valueType: 'dateTimeRange',
      render: (_, record) => formatTimestamp(record.lastupdatetime),
      search: {
        transform: (value: any) => {
          if (Array.isArray(value) && value.length === 2) {
            return {
              startTime: value[0],
              endTime: value[1],
            };
          }
          return {};
        },
      },
      // 默认按反馈时间降序排序
      defaultSortOrder: 'descend',
      sorter: (a, b) => a.lastupdatetime - b.lastupdatetime,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
      copyable: true,
    },
    {
      title: 'Session ID',
      dataIndex: 'sessionId',
      width: 150,
      search: false,
      copyable: true,
    },
    {
      title: '挽留原因',
      dataIndex: 'type',
      width: 300,
      search: false,
      render: (_, record) => {
        return renderLeaveReasonTags(record.type);
      },
    },
    {
      title: '反馈内容',
      dataIndex: 'content',
      width: 300,
      search: false,
      ellipsis: {
        showTitle: false,
      },
      copyable: true,
      render: (_, record) => (
        <Tooltip title={record.content} placement="topLeft">
          <div
            style={{
              maxWidth: 280,
              cursor: 'pointer',
              wordBreak: 'break-all',
              lineHeight: '1.4',
            }}
          >
            {record.content}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '图片列表',
      dataIndex: 'imgUrl',
      width: 200,
      search: false,
      render: (_, record) => {
        const imageUrls = parseImageUrls(record.imgUrl);

        if (imageUrls.length === 0) {
          return <Typography.Text type="secondary">无</Typography.Text>;
        }

        return (
          <Space wrap>
            {imageUrls.slice(0, 3).map((url, index) => (
              <Image
                key={index}
                width={40}
                height={40}
                src={url}
                style={{
                  objectFit: 'cover',
                  borderRadius: 4,
                  border: '1px solid #d9d9d9'
                }}
                preview={{
                  src: url,
                }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
            ))}
            {imageUrls.length > 3 && (
              <Typography.Text type="secondary">
                +{imageUrls.length - 3}
              </Typography.Text>
            )}
          </Space>
        );
      },
    },
    {
      title: '视频',
      dataIndex: 'videoUrl',
      width: 100,
      search: false,
      render: (_, record) => {
        if (!record.videoUrl) {
          return <Typography.Text type="secondary">无</Typography.Text>;
        }

        return (
          <Button
            type="link"
            size="small"
            onClick={() => {
              window.open(record.videoUrl, '_blank');
            }}
          >
            查看视频
          </Button>
        );
      },
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <ProTable<FeedbackRecord, FeedbackQueryParams>
        headerTitle="AI点餐挽留统计"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps) => [
            <Button
              key="search"
              type="primary"
              onClick={() => {
                formProps?.form?.submit();
              }}
            >
              查询
            </Button>,
            <Button
              key="reset"
              danger
              onClick={() => {
                formProps?.form?.resetFields();
                formProps?.form?.submit();
              }}
            >
              重置
            </Button>,
          ],
        }}
        toolBarRender={() => [
          <Button
            key="export"
            icon={<ExportOutlined />}
            onClick={() => handleExport(true)}
            disabled={selectedRowKeys.length > 0}
          >
            导出全部
          </Button>,
          <Space key="toolbar" align="center">
            <Typography.Text>环境切换：</Typography.Text>
            <Typography.Text type={isProduction ? 'success' : 'warning'}>
              {isProduction ? '生产环境' : '测试环境'}
            </Typography.Text>
            <Switch
              checked={isProduction}
              onChange={(checked) => {
                setIsProduction(checked);
                // 切换环境后自动刷新数据
                actionRef.current?.reload();
              }}
              checkedChildren="生产"
              unCheckedChildren="测试"
            />
          </Space>
        ]}
        tableAlertRender={() => <TableAlertRender />}
        request={async (params) => {
          try {
            console.log('Request params:', params);
            
            // 转换参数格式
            const queryParams: FeedbackQueryParams = {
              pageNo: params.current || 1,
              pageSize: params.pageSize || 20,
            };

            // 添加手机号搜索
            if (params.phone) {
              queryParams.phone = String(params.phone);
            }

            // 添加时间范围搜索
            if (params.startTime) {
              queryParams.startTime = String(params.startTime);
            }
            if (params.endTime) {
              queryParams.endTime = String(params.endTime);
            }

            // 保存当前查询参数，用于导出功能
            setCurrentQueryParams(queryParams);

            console.log('Query params:', queryParams);

            const response = await queryFeedback(queryParams, isProduction);

            console.log('API Response:', response);

            if (response && response.errCode === 0) {
              // 过滤feedbackType为"leaveReason"的数据
              const filteredData = response.data.list.filter(item =>
                item.feedbackType === 'leaveReason'
              );

              return {
                data: filteredData,
                success: true,
                total: filteredData.length, // 使用过滤后的数据长度
              };
            } else {
              message.error('获取挽留统计数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('Error fetching leave reason data:', error);
            message.error('获取挽留统计数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows as FeedbackRecord[]);
          },
        }}
      />
    </div>
  );
};

export default AiLeaveReasonList;
