---
description: AI点餐挽留统计页面功能规范与实现记录
globs: 
alwaysApply: false
---
# AI点餐挽留统计页面 - 功能规范与实现记录

## 页面概述
- **功能定位**: AI点餐挽留原因数据统计分析系统
- **访问路径**: `/ai-leave-reason`
- **权限要求**: admin和user角色均可访问
- **主要用户**: 系统管理员、数据分析员
- **数据源**: KFC AI点餐服务反馈接口（feedbackType="leaveReason"）

## 功能特性
### 核心功能
- [x] 挽留原因数据列表展示（时间、ID、手机号、挽留原因等）
- [x] 多条件搜索（手机号、时间范围）
- [x] 环境切换（生产/测试环境）
- [x] 数据导出Excel功能
- [x] 图片预览和视频链接处理
- [x] 手机号和Session ID复制功能
- [x] 挽留原因类型映射显示

### 数据展示列
| 字段 | 显示名称 | 特殊功能 |
|------|----------|----------|
| feedback_time | 反馈时间 | 默认降序排序 |
| id | ID | - |
| mobile | 手机号 | 支持复制 |
| session_id | Session ID | 支持复制 |
| leave_reason | 挽留原因 | 数字映射为文本，支持多选显示 |
| feedback_content | 反馈内容 | Tooltip+复制功能 |
| image_list | 图片列表 | 缩略图预览 |
| video | 视频 | 外部链接 |

### 挽留原因映射表
```typescript
const LEAVE_REASON_MAP: Record<string, string> = {
  'A1': '只是随便逛逛，暂无点餐需求',
  'A2': '理解需求不够准确',
  'A3': '推荐餐品不符合我的口味偏好',
  'A4': '推荐餐品价格偏高',
  'A5': '操作流程复杂'
};
```

### 数据过滤逻辑
- **feedbackType过滤**: 固定过滤`feedbackType="leaveReason"`的数据
- **type字段解析**: 支持逗号分隔的多选值（如"1,2,3"）
- **映射显示**: 将数字代码转换为对应的中文描述

## 技术实现架构
### API配置
```typescript
// 使用统一的API函数，在前端进行数据过滤
import { queryFeedback } from '@/services/ant-design-pro/feedback';

// 在request函数中过滤数据
const response = await queryFeedback(queryParams, isProduction);
if (response && response.errCode === 0) {
  // 过滤feedbackType为"leaveReason"的数据
  const filteredData = response.data.list.filter(item =>
    item.feedbackType === 'leaveReason'
  );

  return {
    data: filteredData,
    success: true,
    total: filteredData.length,
  };
}
```

### 路由配置
```typescript
{
  name: 'aiLeaveReason',
  icon: 'customerServiceOutlined',
  path: '/ai-leave-reason',
  component: './AiLeaveReason',
}
```

### 国际化配置
```typescript
// 中文
'menu.aiLeaveReason': 'AI点餐挽留统计'

// 英文
'menu.aiLeaveReason': 'AI Ordering Leave Reason Statistics'
```

## 与AI点餐反馈统计的差异
### 数据过滤差异
- **AI点餐反馈统计**: 前端过滤`feedbackType === '' || feedbackType === undefined || feedbackType === null`
- **AI点餐挽留统计**: 前端过滤`feedbackType === 'leaveReason'`

### 问题类别列差异
- **AI点餐反馈统计**: 直接显示`type`字段原始值
- **AI点餐挽留统计**: 解析逗号分隔的`type`字段，映射为中文描述

### 页面标题差异
- **AI点餐反馈统计**: "AI点餐反馈统计"
- **AI点餐挽留统计**: "AI点餐挽留统计"

### 导出文件名差异
- **AI点餐反馈统计**: `ai_feedback_YYYY_MM_DD_HH_MM_SS.xlsx`
- **AI点餐挽留统计**: `ai_leave_reason_YYYY_MM_DD_HH_MM_SS.xlsx`

## 实现细节
### type字段解析逻辑
```typescript
const parseLeaveReasonType = (type: string): string => {
  if (!type) return '未知';
  
  // 如果type是逗号分隔的字符串，解析每个数字并映射
  const typeNumbers = type.split(',').map(num => num.trim()).filter(num => num);
  const mappedReasons = typeNumbers.map(num => LEAVE_REASON_MAP[num] || `未知类型(${num})`);
  
  return mappedReasons.join(', ');
};
```

### 导出功能增强
- 支持全量导出和选中导出
- 自动处理type字段的映射转换
- 分页获取大量数据时的进度提示
- 专用的文件命名规则

### 搜索功能
- 继承原有的手机号搜索功能
- 继承原有的时间范围搜索功能
- 环境切换功能（生产/测试）

## 开发记录
### v1.0.0 (2024-12-19)
- [x] 创建基础页面结构
- [x] 实现feedbackType="leaveReason"数据过滤
- [x] 实现type字段的逗号分隔解析和映射
- [x] 配置路由和国际化菜单
- [x] 实现导出功能
- [x] 添加环境切换功能

### API服务扩展
- [x] 扩展`FeedbackRecord`类型，添加`feedbackType`字段
- [x] 扩展`FeedbackQueryParams`类型，添加`feedbackType`参数
- [x] 创建专用的`queryLeaveReasonFeedback`函数
- [x] 修改原有`queryFeedback`函数支持feedbackType过滤

### 页面组件实现
- [x] 基于`AiFeedback`页面创建`AiLeaveReason`页面
- [x] 实现挽留原因映射表和解析逻辑
- [x] 自定义表格列渲染，支持多选原因显示
- [x] 继承所有原有功能（搜索、导出、环境切换等）

## 测试要点
### 功能测试
- [ ] 页面正常加载和数据展示
- [ ] 挽留原因映射显示正确
- [ ] 搜索功能正常工作
- [ ] 导出功能正常工作
- [ ] 环境切换功能正常工作

### 数据验证
- [ ] 确认只显示feedbackType="leaveReason"的数据
- [ ] 验证type字段的逗号分隔解析
- [ ] 验证映射表的正确性
- [ ] 验证多选原因的显示效果

### 兼容性测试
- [ ] 与原有AI点餐反馈统计页面功能不冲突
- [ ] 菜单导航正常工作
- [ ] 权限控制正常工作

## 维护说明
### 挽留原因映射表更新
如需添加新的挽留原因类型，请更新`LEAVE_REASON_MAP`常量：
```typescript
const LEAVE_REASON_MAP: Record<string, string> = {
  'A1': '只是随便逛逛，暂无点餐需求',
  'A2': '理解需求不够准确',
  'A3': '推荐餐品不符合我的口味偏好',
  'A4': '推荐餐品价格偏高',
  'A5': '操作流程复杂'
};
```

### API参数调整
如需修改feedbackType过滤条件，请更新`queryLeaveReasonFeedback`函数中的固定值。

### 样式调整
当前使用内联样式，如需优化可考虑提取到CSS文件中。
