import { supabaseClient } from './index';

// 创建一个函数来检查环境变量是否正确加载
export async function checkSupabaseConnection() {
  // 记录当前使用的 URL（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('使用的 Supabase URL:', process.env.SUPABASE_URL ? '已配置' : '未配置');
  }
  
  try {
    // 尝试连接到 Supabase
    const { error } = await supabaseClient.from('_test_connection').select('*').limit(1);
    
    if (error) {
      console.error('Supabase 连接测试失败:', error.message);
      return {
        success: false,
        message: `连接失败: ${error.message}`,
        envVars: {
          SUPABASE_URL_CONFIGURED: Boolean(process.env.SUPABASE_URL),
          SUPABASE_ANON_KEY_CONFIGURED: Boolean(process.env.SUPABASE_ANON_KEY),
          SUPABASE_SERVICE_KEY_CONFIGURED: Boolean(process.env.SUPABASE_SERVICE_KEY)
        }
      };
    }

    return {
      success: true,
      message: '成功连接到 Supabase',
      envVars: {
        SUPABASE_URL_CONFIGURED: Boolean(process.env.SUPABASE_URL),
        SUPABASE_ANON_KEY_CONFIGURED: Boolean(process.env.SUPABASE_ANON_KEY),
        SUPABASE_SERVICE_KEY_CONFIGURED: Boolean(process.env.SUPABASE_SERVICE_KEY)
      }
    };
  } catch (err: any) {
    console.error('执行连接测试时发生错误:', err);
    return {
      success: false,
      message: `执行出错: ${err.message}`,
      envVars: {
        SUPABASE_URL_CONFIGURED: Boolean(process.env.SUPABASE_URL),
        SUPABASE_ANON_KEY_CONFIGURED: Boolean(process.env.SUPABASE_ANON_KEY),
        SUPABASE_SERVICE_KEY_CONFIGURED: Boolean(process.env.SUPABASE_SERVICE_KEY)
      }
    };
  }
}

// 直接导出当前环境变量状态，不执行连接测试
export function getSupabaseEnvStatus() {
  return {
    SUPABASE_URL_CONFIGURED: Boolean(process.env.SUPABASE_URL),
    SUPABASE_ANON_KEY_CONFIGURED: Boolean(process.env.SUPABASE_ANON_KEY),
    SUPABASE_SERVICE_KEY_CONFIGURED: Boolean(process.env.SUPABASE_SERVICE_KEY)
  };
}

export default {
  checkSupabaseConnection,
  getSupabaseEnvStatus
}; 