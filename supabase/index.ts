import { createClient } from '@supabase/supabase-js';

// 配置信息 - 首先尝试从环境变量中获取，如果不存在则使用默认值
// 注意：在生产环境中应该首先确保环境变量已正确加载
// 以下默认值仅用于开发环境和备用方案
const supabaseUrl = process.env.SUPABASE_URL || '';

// .env中的SUPABASE_ANON_KEY
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

// .env中的SUPABASE_SERVICE_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';

// 记录当前使用的配置（不包含完整的密钥）
// 注意：生产环境中不应输出敏感配置信息到控制台
if (process.env.NODE_ENV === 'development') {
  console.log('Supabase 配置状态:');
  console.log('- URL已配置:', Boolean(supabaseUrl));
  console.log('- 匿名密钥已配置:', Boolean(process.env.SUPABASE_ANON_KEY));
  console.log('- 服务密钥已配置:', Boolean(process.env.SUPABASE_SERVICE_KEY));
}

// 如果初始化失败，输出详细信息便于调试
if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.warn('Supabase 配置不完整:');
  if (!supabaseUrl) console.warn('- SUPABASE_URL 未设置');
  if (!supabaseAnonKey) console.warn('- SUPABASE_ANON_KEY 未设置');
  if (!supabaseServiceKey) console.warn('- SUPABASE_SERVICE_KEY 未设置'); 
}

// 创建Supabase客户端实例
export const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
});

// 使用服务角色权限的客户端（用于需要更高权限的操作）
// 注意：服务密钥可以绕过所有RLS策略，应该仅在服务端使用，且不暴露给客户端
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false, // 服务角色不需要刷新令牌
    persistSession: false,   // 服务角色不需要持久化会话
  },
});

// 类型安全的表名
export const TABLES = {
  ADDRESSES: 'address_baidu',
  TENCENT_ADDRESSES: 'address_tencent',
  USER_PROFILES: 'user_profiles',
};

// 公共错误处理
export const handleSupabaseError = (error: any) => {
  console.error('Supabase Error:', error);
  return {
    success: false,
    message: error.message || '操作失败',
    data: null,
  };
};

export default supabaseClient; 